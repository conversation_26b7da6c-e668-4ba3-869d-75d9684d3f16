#!/bin/bash

# RagnaDocker Reset Impact Checker
# This script shows exactly what would be affected by a factory reset
# WITHOUT actually deleting anything

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Project name for Docker resources
PROJECT_NAME="ragnadocker"

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                RagnaDocker Reset Impact Analysis             ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${BLUE}▶ $1${NC}"
}

# Function to print items that would be deleted
print_delete() {
    echo -e "${RED}  ❌ $1${NC}"
}

# Function to print items that would be preserved
print_preserve() {
    echo -e "${GREEN}  ✅ $1${NC}"
}

# Function to print warnings
print_warning() {
    echo -e "${YELLOW}  ⚠️  $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${CYAN}  ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ] || [ ! -f "README.md" ]; then
    echo -e "${RED}❌ This script must be run from the RagnaDocker project root directory${NC}"
    exit 1
fi

echo -e "${YELLOW}This analysis shows what would be affected by a factory reset.${NC}"
echo -e "${YELLOW}No actual changes will be made to your system.${NC}"
echo ""

# Analyze Docker containers
print_section "Docker Containers Analysis"
CONTAINERS=$(docker-compose ps -q 2>/dev/null || true)
if [ ! -z "$CONTAINERS" ]; then
    echo -e "${RED}  The following containers would be STOPPED and REMOVED:${NC}"
    docker-compose ps --format "table {{.Name}}\t{{.State}}\t{{.Ports}}" 2>/dev/null || true
else
    print_info "No running containers found"
fi

# Check for any other project-related containers
OTHER_CONTAINERS=$(docker ps -a --filter "name=${PROJECT_NAME}" --format "{{.Names}}" 2>/dev/null || true)
if [ ! -z "$OTHER_CONTAINERS" ]; then
    echo -e "${RED}  Additional project containers that would be removed:${NC}"
    echo "$OTHER_CONTAINERS" | while read container; do
        print_delete "$container"
    done
fi
echo ""

# Analyze Docker images
print_section "Docker Images Analysis"
IMAGES=$(docker images --filter "reference=${PROJECT_NAME}*" --format "{{.Repository}}:{{.Tag}}\t{{.Size}}" 2>/dev/null || true)
if [ ! -z "$IMAGES" ]; then
    echo -e "${RED}  The following images would be DELETED:${NC}"
    echo "$IMAGES" | while read image; do
        print_delete "$image"
    done
else
    print_info "No project-specific images found"
fi

# Check for related images
RELATED_IMAGES=$(docker images --filter "reference=*fluxcp*" --filter "reference=*rathena*" --format "{{.Repository}}:{{.Tag}}\t{{.Size}}" 2>/dev/null | head -5 || true)
if [ ! -z "$RELATED_IMAGES" ]; then
    echo -e "${YELLOW}  Related images that might be removed:${NC}"
    echo "$RELATED_IMAGES" | while read image; do
        print_warning "$image"
    done
fi
echo ""

# Analyze Docker volumes
print_section "Docker Volumes Analysis"
VOLUMES=$(docker volume ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}\t{{.Driver}}" 2>/dev/null || true)
if [ ! -z "$VOLUMES" ]; then
    echo -e "${RED}  The following volumes would be DELETED (DATA LOSS):${NC}"
    echo "$VOLUMES" | while read volume; do
        print_delete "$volume"
    done
fi

# Check for compose volumes
CURRENT_DIR=$(basename "$PWD")
COMPOSE_VOLUMES=$(docker volume ls --filter "name=${CURRENT_DIR}" --format "{{.Name}}\t{{.Driver}}" 2>/dev/null || true)
if [ ! -z "$COMPOSE_VOLUMES" ]; then
    echo -e "${RED}  Docker Compose volumes that would be DELETED:${NC}"
    echo "$COMPOSE_VOLUMES" | while read volume; do
        print_delete "$volume"
    done
fi

if [ -z "$VOLUMES" ] && [ -z "$COMPOSE_VOLUMES" ]; then
    print_info "No project volumes found"
fi
echo ""

# Analyze Docker networks
print_section "Docker Networks Analysis"
NETWORKS=$(docker network ls --filter "name=${PROJECT_NAME}" --format "{{.Name}}\t{{.Driver}}" 2>/dev/null || true)
if [ ! -z "$NETWORKS" ]; then
    echo -e "${RED}  The following networks would be DELETED:${NC}"
    echo "$NETWORKS" | while read network; do
        print_delete "$network"
    done
else
    print_info "No project networks found"
fi
echo ""

# Analyze source code directories
print_section "Source Code Analysis"
if [ -d "rathena" ]; then
    RATHENA_FILES=$(find rathena -type f ! -name "Dockerfile" ! -name "start.sh" 2>/dev/null | wc -l)
    if [ "$RATHENA_FILES" -gt 0 ]; then
        print_delete "rAthena source code ($RATHENA_FILES files)"
        print_preserve "rathena/Dockerfile"
        print_preserve "rathena/start.sh"
    else
        print_info "rAthena directory contains only custom files"
    fi
else
    print_info "rAthena directory not found"
fi

if [ -d "fluxcp" ]; then
    FLUXCP_FILES=$(find fluxcp -type f ! -name "Dockerfile*" ! -name "*.conf" ! -name "*.ini" 2>/dev/null | wc -l)
    if [ "$FLUXCP_FILES" -gt 0 ]; then
        print_delete "FluxCP source code ($FLUXCP_FILES files)"
        print_preserve "fluxcp/Dockerfile*"
        print_preserve "fluxcp/*.conf"
        print_preserve "fluxcp/*.ini"
    else
        print_info "FluxCP directory contains only custom files"
    fi
else
    print_info "FluxCP directory not found"
fi
echo ""

# Analyze logs and data
print_section "Logs and Data Analysis"
if [ -d "logs" ]; then
    LOG_FILES=$(find logs -type f 2>/dev/null | wc -l)
    if [ "$LOG_FILES" -gt 0 ]; then
        print_delete "Log files ($LOG_FILES files)"
    else
        print_info "No log files found"
    fi
else
    print_info "Logs directory not found"
fi

if [ -d "sql" ]; then
    BACKUP_FILES=$(find sql -name "*.sql" ! -name "01-init-database.sql" 2>/dev/null | wc -l)
    if [ "$BACKUP_FILES" -gt 0 ]; then
        print_delete "SQL backup files ($BACKUP_FILES files)"
        print_preserve "sql/01-init-database.sql"
    else
        print_info "No SQL backup files found"
        print_preserve "sql/01-init-database.sql"
    fi
else
    print_info "SQL directory not found"
fi
echo ""

# Show what would be preserved
print_section "Files That Would Be PRESERVED"
PRESERVED_FILES=(
    "docker-compose.yml"
    "README.md"
    "Makefile"
    "start.sh"
    "reset-project.sh"
    "*.md files"
    "config/ directory"
    ".env.example"
    ".gitignore"
)

for file in "${PRESERVED_FILES[@]}"; do
    if [[ "$file" == *"*"* ]] || [ -f "$file" ] || [ -d "$file" ]; then
        print_preserve "$file"
    fi
done
echo ""

# Calculate approximate disk space that would be recovered
print_section "Estimated Disk Space Recovery"
DOCKER_SIZE=0
if command -v docker &> /dev/null; then
    # Estimate Docker images size
    if [ ! -z "$IMAGES" ]; then
        IMAGE_SIZE=$(docker images --filter "reference=${PROJECT_NAME}*" --format "{{.Size}}" 2>/dev/null | sed 's/[^0-9.]//g' | awk '{sum += $1} END {print sum}' || echo "0")
        print_info "Docker images: ~${IMAGE_SIZE:-Unknown} MB"
    fi
    
    # Estimate volumes size (rough estimate)
    if [ ! -z "$VOLUMES" ] || [ ! -z "$COMPOSE_VOLUMES" ]; then
        print_info "Docker volumes: ~100-500 MB (estimated)"
    fi
fi

# Estimate source code size
if [ -d "rathena" ] && [ "$RATHENA_FILES" -gt 0 ]; then
    RATHENA_SIZE=$(du -sm rathena 2>/dev/null | cut -f1 || echo "Unknown")
    print_info "rAthena source: ~${RATHENA_SIZE} MB"
fi

if [ -d "fluxcp" ] && [ "$FLUXCP_FILES" -gt 0 ]; then
    FLUXCP_SIZE=$(du -sm fluxcp 2>/dev/null | cut -f1 || echo "Unknown")
    print_info "FluxCP source: ~${FLUXCP_SIZE} MB"
fi

if [ -d "logs" ] && [ "$LOG_FILES" -gt 0 ]; then
    LOGS_SIZE=$(du -sm logs 2>/dev/null | cut -f1 || echo "Unknown")
    print_info "Log files: ~${LOGS_SIZE} MB"
fi

echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                    Analysis Complete                         ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${BLUE}To proceed with the actual reset:${NC}"
echo "  ./reset-project.sh"
echo ""
echo -e "${BLUE}To rebuild after reset:${NC}"
echo "  ./start.sh"
echo ""
echo -e "${YELLOW}⚠️  Remember: The reset operation is IRREVERSIBLE!${NC}"
echo -e "${YELLOW}⚠️  Always backup important data before proceeding!${NC}"
